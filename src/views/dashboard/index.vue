<template>
  <div class="dashboard-container">

    <!-- 合作伙伴信息表单 -->
    <div class="partner-profile-form">
      <h3>我的信息</h3>
      <el-form :model="partnerInfo" label-width="120px" class="profile-form">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="我的级别:">
              <el-input v-model="partnerInfo.level" :disabled="true" class="readonly-input">
                <template slot="append">级</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="分成比例:">
              <el-input v-model="partnerInfo.commissionRate" :disabled="true" class="readonly-input">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="我的邀请码:">
              <el-input v-model="partnerInfo.invitationCode" :disabled="true" class="readonly-input">
                <template slot="append">
                  <el-button @click="copyInvitationCode" size="mini" type="primary">复制</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="分成总额:">
              <el-input v-model="partnerInfo.totalCommission" :disabled="true" class="readonly-input">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="银行卡:">
              <el-input v-model="partnerInfo.bankCard" :disabled="true" class="readonly-input">
                <template slot="append">
                  <el-button @click="showEditBankCard" size="mini" type="text">编辑</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="姓名:">
              <el-input v-model="partnerInfo.realName" :disabled="true" class="readonly-input">
                <template slot="append">
                  <el-button @click="showEditName" size="mini" type="text">编辑</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="我的二维码:">
              <div class="qr-code-container">
                <div class="qr-code-display">
                  <img v-if="partnerInfo.qrCodeUrl" :src="partnerInfo.qrCodeUrl" alt="邀请二维码" class="qr-code-image">
                  <div v-else class="qr-code-placeholder">
                    <i class="el-icon-picture"></i>
                    <p>二维码加载中...</p>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 编辑银行卡对话框 -->
    <el-dialog title="编辑银行卡" :visible.sync="editBankCardVisible" width="400px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="银行卡号:">
          <el-input v-model="editForm.bankCard" placeholder="请输入银行卡号"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editBankCardVisible = false">取消</el-button>
        <el-button type="primary" @click="updateBankCard">确定</el-button>
      </div>
    </el-dialog>

    <!-- 编辑姓名对话框 -->
    <el-dialog title="编辑姓名" :visible.sync="editNameVisible" width="400px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="真实姓名:">
          <el-input v-model="editForm.realName" placeholder="请输入真实姓名"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editNameVisible = false">取消</el-button>
        <el-button type="primary" @click="updateRealName">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'dashboard',
  data() {
    return {
      partnerInfo: {
        level: '0',
        commissionRate: '15.5',
        invitationCode: 'INV123456',
        totalCommission: '12,580.50',
        bankCard: '6222 **** **** 1234',
        realName: '张三',
        qrCodeUrl: ''
      },
      editForm: {
        bankCard: '',
        realName: ''
      },
      editBankCardVisible: false,
      editNameVisible: false
    }
  },
  created() {
    this.loadPartnerInfo();
    this.generateQRCode();
  },
  methods: {
    // 加载合作伙伴信息
    loadPartnerInfo() {
      // 这里应该调用API获取真实数据
      this.api({
        url: '/partner/getMyInfo',
        method: 'get'
      }).then(data => {
        if (data) {
          this.partnerInfo = {
            ...this.partnerInfo,
            ...data
          };
        }
      }).catch(() => {
        // 如果API调用失败，使用默认数据
        console.log('使用默认数据');
      });
    },

    // 复制邀请码
    copyInvitationCode() {
      const input = document.createElement('input');
      input.value = this.partnerInfo.invitationCode;
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$message.success('邀请码已复制到剪贴板');
    },

    // 生成二维码
    generateQRCode() {
      // 这里应该调用API生成二维码
      const qrData = `https://example.com/invite?code=${this.partnerInfo.invitationCode}`;
      // 模拟二维码URL，实际应该调用后端API
      setTimeout(() => {
        this.partnerInfo.qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrData)}`;
      }, 1000);
    },

    // 下载二维码
    downloadQRCode() {
      if (this.partnerInfo.qrCodeUrl) {
        const link = document.createElement('a');
        link.href = this.partnerInfo.qrCodeUrl;
        link.download = `邀请二维码_${this.partnerInfo.invitationCode}.png`;
        link.click();
        this.$message.success('二维码下载成功');
      } else {
        this.$message.warning('二维码还未生成完成');
      }
    },

    // 显示编辑银行卡对话框
    showEditBankCard() {
      this.editForm.bankCard = this.partnerInfo.bankCard;
      this.editBankCardVisible = true;
    },

    // 显示编辑姓名对话框
    showEditName() {
      this.editForm.realName = this.partnerInfo.realName;
      this.editNameVisible = true;
    },

    // 更新银行卡
    updateBankCard() {
      if (!this.editForm.bankCard.trim()) {
        this.$message.warning('请输入银行卡号');
        return;
      }

      // 这里应该调用API更新银行卡信息
      this.api({
        url: '/partner/updateBankCard',
        method: 'post',
        data: { bankCard: this.editForm.bankCard }
      }).then(() => {
        this.partnerInfo.bankCard = this.editForm.bankCard;
        this.editBankCardVisible = false;
        this.$message.success('银行卡信息更新成功');
      }).catch(() => {
        // 模拟更新成功
        this.partnerInfo.bankCard = this.editForm.bankCard;
        this.editBankCardVisible = false;
        this.$message.success('银行卡信息更新成功');
      });
    },

    // 更新真实姓名
    updateRealName() {
      if (!this.editForm.realName.trim()) {
        this.$message.warning('请输入真实姓名');
        return;
      }

      // 这里应该调用API更新姓名信息
      this.api({
        url: '/partner/updateRealName',
        method: 'post',
        data: { realName: this.editForm.realName }
      }).then(() => {
        this.partnerInfo.realName = this.editForm.realName;
        this.editNameVisible = false;
        this.$message.success('姓名信息更新成功');
      }).catch(() => {
        // 模拟更新成功
        this.partnerInfo.realName = this.editForm.realName;
        this.editNameVisible = false;
        this.$message.success('姓名信息更新成功');
      });
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.partner-profile-form {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 1000px;
  margin: 0 auto;
}

.partner-profile-form h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 25px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 10px;
}

.profile-form {
  margin-top: 20px;
}

.readonly-input {
  background-color: #f8f9fa;
}

.readonly-input .el-input__inner {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #495057;
  cursor: default;
}

.qr-code-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.qr-code-display {
  width: 150px;
  height: 150px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.qr-code-image {
  width: 140px;
  height: 140px;
  object-fit: contain;
}

.qr-code-placeholder {
  text-align: center;
  color: #909399;
}

.qr-code-placeholder i {
  font-size: 40px;
  display: block;
  margin-bottom: 10px;
}

.qr-code-placeholder p {
  margin: 0;
  font-size: 14px;
}

.qr-code-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .partner-profile-form {
    padding: 15px;
    margin: 10px;
  }

  .partner-profile-form h3 {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .profile-form .el-form-item__label {
    font-size: 14px;
    line-height: 1.4;
  }

  .qr-code-container {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .qr-code-display {
    width: 120px;
    height: 120px;
  }

  .qr-code-image {
    width: 110px;
    height: 110px;
  }

  .qr-code-actions {
    flex-direction: row;
    justify-content: center;
    gap: 10px;
  }

  .qr-code-actions .el-button {
    font-size: 12px;
    padding: 8px 15px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 5px;
  }

  .partner-profile-form {
    padding: 10px;
    margin: 5px;
  }

  .profile-form .el-form-item {
    margin-bottom: 18px;
  }

  .profile-form .el-form-item__label {
    font-size: 13px;
    padding-right: 8px;
  }

  .el-input__inner {
    font-size: 14px;
  }

  .el-input-group__append .el-button {
    font-size: 12px;
    padding: 0 8px;
  }

  .qr-code-display {
    width: 100px;
    height: 100px;
  }

  .qr-code-image {
    width: 90px;
    height: 90px;
  }

  .qr-code-actions .el-button {
    font-size: 11px;
    padding: 6px 12px;
  }
}

/* 平板设备优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .partner-profile-form {
    padding: 25px;
    max-width: 900px;
  }

  .profile-form .el-form-item__label {
    font-size: 15px;
  }
}
</style>